<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>XPlayClass</class>
 <widget class="QWidget" name="XPlayClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>XPlay</string>
  </property>
  <property name="windowIcon">
   <iconset resource="xplay.qrc">
    <normaloff>:/XPlay/Resources/logo.ico</normaloff>:/XPlay/Resources/logo.ico</iconset>
  </property>
  <widget class="VideoWidget" name="openGLWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>600</height>
    </rect>
   </property>
  </widget>
  <widget class="QPushButton" name="openButton">
   <property name="geometry">
    <rect>
     <x>290</x>
     <y>500</y>
     <width>61</width>
     <height>71</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton:!hover{border-image: url(:/XPlay/Resources/open_normal.png);}
QPushButton:hover{border-image: url(:/XPlay/Resources/open_hot.png);}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="playButton">
   <property name="geometry">
    <rect>
     <x>390</x>
     <y>510</y>
     <width>61</width>
     <height>61</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton:!hover{border-image: url(:/XPlay/Resources/play_normal.png);}
QPushButton:hover{border-image: url(:/XPlay/Resources/play_hot.png);}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="totaltime">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>520</y>
     <width>111</width>
     <height>41</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">color: rgb(255, 255, 255);
font: 75 16pt &quot;Adobe 黑体 Std R&quot;;</string>
   </property>
   <property name="text">
    <string>000:00</string>
   </property>
  </widget>
  <widget class="QLabel" name="playtime">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>520</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">color: rgb(255, 255, 255);
font: 75 16pt &quot;Adobe 黑体 Std R&quot;;</string>
   </property>
   <property name="text">
    <string>000:00</string>
   </property>
  </widget>
  <widget class="QLabel" name="sp">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>520</y>
     <width>21</width>
     <height>41</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">color: rgb(255, 255, 255);
font: 75 16pt &quot;Adobe 黑体 Std R&quot;;</string>
   </property>
   <property name="text">
    <string>/</string>
   </property>
  </widget>
  <widget class="XSlider" name="playslider">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>470</y>
     <width>741</width>
     <height>22</height>
    </rect>
   </property>
   <property name="maximum">
    <number>999</number>
   </property>
   <property name="pageStep">
    <number>100</number>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>VideoWidget</class>
   <extends>QOpenGLWidget</extends>
   <header>videowidget.h</header>
  </customwidget>
  <customwidget>
   <class>XSlider</class>
   <extends>QSlider</extends>
   <header>xslider.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="xplay.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>openButton</sender>
   <signal>clicked()</signal>
   <receiver>XPlayClass</receiver>
   <slot>open()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>322</x>
     <y>543</y>
    </hint>
    <hint type="destinationlabel">
     <x>947</x>
     <y>231</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>playslider</sender>
   <signal>sliderPressed()</signal>
   <receiver>XPlayClass</receiver>
   <slot>sliderPress()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>160</x>
     <y>477</y>
    </hint>
    <hint type="destinationlabel">
     <x>854</x>
     <y>95</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>playslider</sender>
   <signal>sliderReleased()</signal>
   <receiver>XPlayClass</receiver>
   <slot>sliderRelease()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>258</x>
     <y>481</y>
    </hint>
    <hint type="destinationlabel">
     <x>829</x>
     <y>260</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>playButton</sender>
   <signal>clicked()</signal>
   <receiver>XPlayClass</receiver>
   <slot>play()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>424</x>
     <y>543</y>
    </hint>
    <hint type="destinationlabel">
     <x>643</x>
     <y>-40</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>open()</slot>
  <slot>sliderPress()</slot>
  <slot>sliderRelease()</slot>
  <slot>play()</slot>
  <slot>resize()</slot>
 </slots>
</ui>
